
body {
  --tt-toolbar-height: 44px;
  --tt-theme-text: var(--tt-gray-light-900);
  --tt-bg-color: var(--tt-gray-light-100);


  // .dark & {
  //   --tt-theme-text: var(--tt-gray-dark-900);
  // }
}

body {
  color: var(--tt-theme-text);
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  padding: 0;
}

html,
body,
#root,
#app {
  height: 100%;
  background-color: var(--tt-bg-color);
}

// body {
//   overflow: hidden;
// }


.content-wrapper {
  height: calc(100% - var(--tt-toolbar-height));
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: block;
    width: 0.5rem;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--tt-scrollbar-color);
    border-radius: 4px;
  }

  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: var(--tt-scrollbar-color) transparent;
}

.simple-editor-content {
  max-width: 640px;
  width: 100%;
  margin: 0 auto;
}

.simple-editor-content .tiptap.ProseMirror {
  padding: 3rem 3rem;
}

@media screen and (max-width: 480px) {
  .simple-editor-content .tiptap.ProseMirror {
    padding: 1rem 1.5rem;
  }
}
