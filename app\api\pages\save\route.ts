import { NextRequest, NextResponse } from 'next/server';
import { StaticPageType } from '@prisma/client';
import { prisma } from '@/lib/prisma';


export async function POST(req: NextRequest) {
  try {
    const { siteId, pages } = await req.json();

    if (!siteId || typeof pages !== 'object') {
      return NextResponse.json({ error: 'Invalid payload' }, { status: 400 });
    }

    const result = await prisma.staticPage.createMany({
      data: Object.entries(pages).map(([key, value]) => ({
        page_type: StaticPageType[key.toUpperCase() as keyof typeof StaticPageType],
        title: key.charAt(0).toUpperCase() + key.slice(1),
        content: value as string,
        site_id: siteId,
      })),
      skipDuplicates: true,
    });
    

    return NextResponse.json({
      success: true,
      saved: result.count,
    });
  } catch (error) {
    console.error('Error saving static pages:', error);
    return NextResponse.json({ error: 'Failed to save static pages' }, { status: 500 });
  }
}
