import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';


export async function GET(req: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
    // get the slug from the path parameter
    const { slug } = await params;
    const { searchParams } = new URL(req.url)
    const domain = searchParams.get('domain')

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain parameter is required' },
        { status: 400 }
      )
    }

    // Query the database for site data
    const site = await prisma.site.findUnique({
      where: { domain }
    })

    if (!site) {
      return NextResponse.json(
        { error: 'Site not found' },
        { status: 404 }
      )
    }

    // get the article from the database
    const article = await prisma.article.findUnique({
        where: { site_id_slug: { slug: slug, site_id: site.id } },
        select: {
          image_url: true,
          title: true,
          created_at: true,
          content: true,
          slug: true,
          article_tags: {
            select: {
              tag: {
                select: {
                  name: true,
                  slug: true
                }
              }
            }
          }
        }
    });
    return NextResponse.json({ success: true, article: article });
}