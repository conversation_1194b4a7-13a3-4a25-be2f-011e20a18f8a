import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { getDummyTitles } from '../../dummyData';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(req: NextRequest) {
  const { contentStyle, tone, niche, language, numberOfArticles } = await req.json();

  try {
    if (process.env.APP_ENV !== 'production') {
      return NextResponse.json({ titles: getDummyTitles(numberOfArticles) }, { status: 200 });
    }

    // Prepare the prompt for OpenAI
    const prompt = `Generate ${numberOfArticles} catchy article titles for a ${niche} website.\nStyle: ${contentStyle}\nTone: ${tone}\nLanguage: ${language}\n\nRespond with a JSON object with a 'titles' key, whose value is an array of strings. Example:\n{\n  \"titles\": [\n    \"Title 1\",\n    \"Title 2\"\n  ]\n}`;

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: "gpt-4-turbo-preview",
      messages: [
        {
          role: "system",
          content: "You are a professional content writer. Generate engaging article titles."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      response_format: { type: "json_object" }
    });

    // Parse the response
    const content = completion.choices[0].message.content;
    if (!content) {
      throw new Error("OpenAI response content is null");
    }
    let titlesArr;
    try {
      const response = JSON.parse(content as string);
      // Accept both { titles: [...] } and just an array
      if (Array.isArray(response)) {
        titlesArr = response;
      } else if (Array.isArray(response.titles)) {
        titlesArr = response.titles;
      } else {
        throw new Error("Unexpected response format from OpenAI");
      }
    } catch (e) {
      throw new Error("Failed to parse OpenAI response as expected titles array");
    }
    const titles = titlesArr.map((title: any, index: number) => ({
      id: index + 1,
      title: title,
    }));
    return NextResponse.json({ titles }, { status: 200 });
  } catch (error) {
    console.error('Error generating titles:', error);

    // Fallback to dummy data in case of error
    const titles = getDummyTitles(numberOfArticles);
    return NextResponse.json({ titles }, { status: 200 });
  }
} 