import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';


export async function POST(req: NextRequest) {
  try {
    const { siteId } = await req.json();

    if (!siteId) {
      return NextResponse.json({ error: 'Site ID is required' }, { status: 400 });
    }

    // Check if site exists
    const site = await prisma.site.findUnique({
      where: { id: siteId },
      select: { id: true, domain: true } // Only select ID to minimize data transfer
    });

    if (!site) {
      return NextResponse.json({ error: 'Invalid site ID' }, { status: 404 });
    }

    return NextResponse.json({ valid: true, domain: site.domain });
  } catch (error) {
    console.error('Error validating site:', error);
    return NextResponse.json({ error: 'Failed to validate site' }, { status: 500 });
  }
} 