import { NextResponse } from 'next/server';
import { Prisma } from '@prisma/client';
import bcrypt from 'bcrypt';
import { prisma } from '@/lib/prisma';


export async function POST(request: Request) {
  const { email, password, first_name, last_name, account_type, phone, teams, linkedin, company, vat, country, state, city, zip, address, accept_terms, username } = await request.json();

  if (!email || !password) {
    return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
  }

  const hashedPassword = await bcrypt.hash(password, 10);

  try {
    // Find or create a default 'User' role
    const defaultRole = await prisma.role.upsert({
      where: { name: 'User' },
      update: {},
      create: {
        name: 'User',
      },
    });

    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        nicename: username,
        role: {
          connect: { id: defaultRole.id },
        },
      },
    });

    // add user meta data
    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'first_name',
        meta_value: first_name,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'last_name',
        meta_value: last_name,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'account_type',
        meta_value: account_type,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'phone',
        meta_value: phone,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'teams',
        meta_value: teams,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'linkedin',
        meta_value: linkedin,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'company',
        meta_value: company,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'vat',
        meta_value: vat,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'country',
        meta_value: country,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'state',
        meta_value: state,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'city',
        meta_value: city,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'zip',
        meta_value: zip,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'address',
        meta_value: address,
      },
    });

    await prisma.userMeta.create({
      data: {
        user_id: user.id,
        meta_key: 'accept_terms',
        meta_value: accept_terms,
      },
    });
    
    return NextResponse.json({ user }, { status: 201 });
  } catch (error) {
    if (
      error instanceof Prisma.PrismaClientKnownRequestError &&
      error.code === 'P2002' &&
      (error.meta?.target as string[])?.includes('email')
    ) {
      return NextResponse.json({ error: 'User with this email already exists' }, { status: 409 }); // Conflict
    } else {
      console.error(error)
      return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 }); // Internal Server Error
    }
  }
} 