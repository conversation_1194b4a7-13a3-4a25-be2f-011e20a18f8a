import {
	S3Client,
	PutObjectCommand,
	ObjectCannedACL
} from "@aws-sdk/client-s3";


const s3 = new S3Client({
	region: process.env.DO_SPACES_REGION,
	endpoint: process.env.DO_SPACES_ORIGIN_ENDPOINT,
	credentials: {
		accessKeyId: process.env.DO_SPACES_KEY!,
		secretAccessKey: process.env.DO_SPACES_SECRET!,
	},
});

export async function uploadToSpaces(body: Buffer<ArrayBufferLike>, filename: string, folder = "generated", contentType: string) {
	console.log(process.env.DO_SPACES_REGION, process.env.DO_SPACES_ORIGIN_ENDPOINT, process.env.DO_SPACES_BUCKET_NAME, process.env.DO_SPACES_CDN_ENDPOINT)
	const uploadParams = {
		Bucket: process.env.DO_SPACES_BUCKET_NAME,
		Key: `${folder}/${filename}`,
		Body: body,
		ACL: ObjectCannedACL.public_read, // Make it publicly accessible
		ContentType: contentType,
	};

	await s3.send(new PutObjectCommand(uploadParams));

	return `${process.env.DO_SPACES_CDN_ENDPOINT}/${uploadParams.Key}`;
}