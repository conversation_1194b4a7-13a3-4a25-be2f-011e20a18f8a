import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { getDummyArticles } from '../../dummyData';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/*
Generate content, meta-description, tags and featured image for given titles. 
*/
export async function POST(req: NextRequest) {
  try {
    const { contentStyle, tone, niche, language, numberOfArticles, titles } = await req.json();

    if (process.env.APP_ENV !== 'production') {
      return NextResponse.json({ articles: getDummyArticles(numberOfArticles) }, { status: 200 });
    }

    // Prepare the prompt for OpenAI
    const prompt = `
      You are a professional content writer and SEO strategist.

      For each of the following article titles, generate an engaging, well-structured article with metadata and image details.

      Write each article with the following requirements:

      1. **Content**: 
        - 1500 to 2500 words
        - Style: ${contentStyle}
        - Tone: ${tone}
        - Language: ${language}
        - Use proper headings (H2, H3) and paragraph formatting
        - Ensure readability and SEO best practices

      2. **Slug**:
        - URL-friendly version of the title
        - Lowercase, words separated by hyphens

      3. **Meta Description**:
        - 2-3 sentence summary (approx. 150-160 characters)
        - Written to maximize click-through rate on search engines

      4. **Tags**:
        - Array of 1-2 relevant tags/keywords

      5. **Image Prompt**:
        - A vivid, specific description of a scene that could be used to generate a relevant high-quality featured image

      ---

      Here are the article titles:

      ${titles.map((title: string, i: number) => `${i + 1}. ${title}`).join('\n')}

      ---

      Please respond in the following strict JSON format:

      {
        "articles": [
          {
            "title": "Article Title",
            "slug": "article-title",
            "metaDescription": "Short meta description here...",
            "tags": ["tag1", "tag2"],
            "content": "Full article content here...",
            "imagePrompt": "A vivid scene of ..."
          },
          ...
        ]
      }
    `;

    const completion = await openai.chat.completions.create({
      model: "gpt-4-turbo-preview",
      messages: [
        {
          role: "system",
          content: "You are a professional content writer. Generate engaging article content."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      response_format: { type: "json_object" }
    });
    const content = completion.choices[0].message.content;
    if (!content) {
      throw new Error("OpenAI response content is null");
    }
    let articlesArr;
    try {
      const response = JSON.parse(content as string);
      // Accept both { articles: [...] } and just an array
      if (Array.isArray(response)) {
        articlesArr = response;
      } else if (Array.isArray(response.articles)) {
        articlesArr = response.articles;
      } else {
        throw new Error("Unexpected response format from OpenAI");
      }
    } catch (e) {
      throw new Error("Failed to parse OpenAI response as expected articles array");
    }
    const articles = articlesArr.map((article: any, index: number) => ({
      id: index + 1,
      title: article.title,
      slug: article.slug,
      content: article.content,
      metaDescription: article.metaDescription,
      tags: article.tags,
      imagePrompt: article.imagePrompt
    }));
    return NextResponse.json({ articles }, { status: 200 });
  } catch (error) {
    console.error('Error generating articles:', error);
    return NextResponse.json({ error: 'Failed to generate articles' }, { status: 500 });
  }
} 