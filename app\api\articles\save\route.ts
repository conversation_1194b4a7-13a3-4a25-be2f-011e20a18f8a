import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

type Article = {
  id: number;
  title: string;
  slug: string;
  content: string;
  metaDescription: string;
  tags: string[];
  featuredImage: string;
  imagePrompt: string;
};

async function generateUniqueSlug(baseSlug: string, siteId: string) {
  let slug = baseSlug;
  let count = 1;
  while (true) {
    const existing = await prisma.article.findFirst({
      where: { slug, site_id: siteId },
      select: { id: true },
    });
    if (!existing) break;
    count += 1;
    slug = `${baseSlug}-${count}`;
  }
  return slug;
}

function randomDateWithinLastThreeMonths() {
  const now = new Date();
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(now.getMonth() - 3);
  const randomTime = threeMonthsAgo.getTime() + Math.random() * (now.getTime() - threeMonthsAgo.getTime());
  return new Date(randomTime);
}

export async function POST(req: NextRequest) {
  const { siteId, articles } = await req.json();
  const savedArticles = [];

  for (const article of articles) {
    // 1. Ensure tags exist and get their IDs
    const tagIds: string[] = [];
    for (const tagName of article.tags) {
      const tagSlug = tagName.toLowerCase().replace(/ /g, '-');
      const tag = await prisma.tag.upsert({
        where: {slug: tagSlug},
        update: {},
        create: { name: tagName, slug: tagSlug },
      });
      tagIds.push(tag.id);
    }

    // 2. Generate a unique slug for the article
    const baseSlug = article.slug || article.title.toLowerCase().replace(/ /g, '-');
    const uniqueSlug = await generateUniqueSlug(baseSlug, siteId);
    const randomDate = randomDateWithinLastThreeMonths();

    // 3. Save the article
    const result = await prisma.article.create({
      data: {
        title: article.title,
        content: article.content,
        image_url: article.featuredImage,
        meta_description: article.metaDescription,
        site_id: siteId,
        slug: uniqueSlug,
        created_at: randomDate,
        updated_at: randomDate,
        article_tags: {
          create: tagIds.map((tagId) => ({
            tag: { connect: { id: tagId } },
          })),
        },
      },
    });

    savedArticles.push(result);
  }

  return NextResponse.json({ success: true, saved: savedArticles.length, articles: savedArticles });
} 