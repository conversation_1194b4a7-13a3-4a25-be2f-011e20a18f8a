import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const email = searchParams.get('email');
    const username = searchParams.get('username');

    if (!email && !username) {
      return NextResponse.json({ error: 'Email or username is required' }, { status: 400 });
    }

    let emailAvailable = undefined;
    let usernameAvailable = undefined;

    if (email) {
      const user = await prisma.user.findUnique({ where: { email } });
      emailAvailable = !user;
    }

    if (username) {
      const user = await prisma.user.findFirst({ where: { nicename: username } });
      usernameAvailable = !user;
    }

    return NextResponse.json({
      emailAvailable,
      usernameAvailable,
    });
  } catch (error) {
    console.error('Error checking availability:', error);
    return NextResponse.json({ error: 'Failed to check availability' }, { status: 500 });
  }
} 