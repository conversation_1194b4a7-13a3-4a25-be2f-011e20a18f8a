import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
    // add 9 dummy articles
    const articles = [
        {
            id: '1',
            title: 'Article 1',
            content: 'Content 1',
            created_at: new Date(),
            updated_at: new Date(),
            image_url: 'https://placehold.co/600x400',
            meta_description: 'Meta description 1',
            meta_keywords: 'Meta keywords 1',
            meta_title: 'Meta title 1',
            published: true,
            slug: 'article-1',
            status: true,
            site_id: 'abcd',
            article_tags: [
                {
                    id: '1',
                    status: true,
                    created_at: new Date(),
                    updated_at: new Date(),
                    tag_id: '1',
                    article_id: '1',
                    tag: {
                        name: 'Tag 1',
                        slug: 'tag-1',
                    },
                },
                {
                    id: '2',
                    status: true,
                    created_at: new Date(),
                    updated_at: new Date(),
                    tag_id: '2',
                    article_id: '2',
                    tag: {
                        name: 'Tag 2',
                        slug: 'tag-2',
                    },
                },

            ],
        },
        {
            id: '2',
            title: 'Article 2',
            content: 'Content 2',
            created_at: new Date(),
            updated_at: new Date(),
            image_url: 'https://placehold.co/600x400',
            meta_description: 'Meta description 2',
            meta_keywords: 'Meta keywords 2',
            meta_title: 'Meta title 2',
            published: true,
            slug: 'article-2',
            status: true,
            site_id: 'abcd',
            article_tags: [],
        },

        {
            id: '3',
            title: 'Article 3',
            content: 'Content 3',
            created_at: new Date(),
            updated_at: new Date(),
            image_url: 'https://placehold.co/600x400',
            meta_description: 'Meta description 4',
            meta_keywords: 'Meta keywords 4',
            meta_title: 'Meta title 4',
            published: true,
            slug: 'article-4',
            status: true,
            site_id: 'abcd',
            article_tags: [],
        },
        {
            id: '4',
            title: 'Article 4',
            content: 'Content 4',
            created_at: new Date(),
            updated_at: new Date(),
            image_url: 'https://placehold.co/600x400',
            meta_description: 'Meta description 5',
            meta_keywords: 'Meta keywords 5',
            meta_title: 'Meta title 5',
            published: true,
            slug: 'article-5',
            status: true,
            site_id: 'abcd',
            article_tags: [],
        },

        {
            id: '5',
            title: 'Article 5',
            content: 'Content 5',
            created_at: new Date(),
            updated_at: new Date(),
            image_url: 'https://placehold.co/600x400',
            meta_description: 'Meta description 5',
            meta_keywords: 'Meta keywords 5',
            meta_title: 'Meta title 5',
            published: true,
            slug: 'article-5',
            status: true,
            site_id: 'abcd',
            article_tags: [],
        },
        {
            id: '6',
            title: 'Article 6',
            content: 'Content 6',
            created_at: new Date(),
            updated_at: new Date(),
            image_url: 'https://placehold.co/600x400',
            meta_description: 'Meta description 6',
            meta_keywords: 'Meta keywords 6',
            meta_title: 'Meta title 6',
            published: true,
            slug: 'article-6',
            status: true,
            site_id: 'abcd',
            article_tags: [],
        },

        {
            id: '7',
            title: 'Article 7',
            content: 'Content 7',
            created_at: new Date(),
            updated_at: new Date(),
            image_url: 'https://placehold.co/600x400',
            meta_description: 'Meta description 7',
            meta_keywords: 'Meta keywords 7',
            meta_title: 'Meta title 7',
            published: true,
            slug: 'article-7',
            status: true,
            site_id: 'abcd',
            article_tags: [],
        },
        {
            id: '8',
            title: 'Article 8',
            content: 'Content 8',
            created_at: new Date(),
            updated_at: new Date(),
            image_url: 'https://placehold.co/600x400',
            meta_description: 'Meta description 8',
            meta_keywords: 'Meta keywords 8',
            meta_title: 'Meta title 8',
            published: true,
            slug: 'article-8',
            status: true,
            site_id: 'abcd',
            article_tags: [],
        },
        {
            id: '9',
            title: 'Article 9',
            content: 'Content 9',
            created_at: new Date(),
            updated_at: new Date(),
            image_url: 'https://placehold.co/600x400',
            meta_description: 'Meta description 9',
            meta_keywords: 'Meta keywords 9',
            meta_title: 'Meta title 9',
            published: true,
            slug: 'article-9',
            status: true,
            site_id: 'abcd',
            article_tags: [],
        }
    ]
    return NextResponse.json({ success: true, articles: articles });
}