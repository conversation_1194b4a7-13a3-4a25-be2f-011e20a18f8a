import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export function useAuth() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  const refreshToken = async () => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      });

      if (!response.ok) return false;

      const data = await response.json();
      return !!data.token;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    }
  };

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/verify', {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        setIsLoggedIn(true);
      } else {
        const refreshed = await refreshToken();
        if (refreshed) {
          setIsLoggedIn(true);
        } else {
          setIsLoggedIn(false);
          router.push('/');
        }
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setIsLoggedIn(false);
      router.push('/');
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setIsLoggedIn(false);
    setLoading(false);
  };


  useEffect(() => {
    checkAuth();

    // Refresh token every 4 minutes (JWT expiry buffer)
    const interval = setInterval(() => {
      refreshToken().then((valid) => {
        if (!valid) {
          setIsLoggedIn(false);
          router.push('/');
        }
      });
    }, 4 * 60 * 1000); // 4 minutes

    return () => clearInterval(interval);
  }, [router]);

  return { isLoggedIn, loading, refreshToken, logout };
}
