import { NextRequest, NextResponse } from 'next/server'
import { getDummyArticles } from '../../articles/dummyData';
import { prisma } from '@/lib/prisma';


export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const domain = searchParams.get('domain')
    const page = searchParams.get('page') || 1
    const take = 12
    const skip = (Number(page) -1) * take

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain parameter is required' },
        { status: 400 }
      )
    }

    // Query the database for site data
    const site = await prisma.site.findUnique({
      where: { domain },
      include: {
        site_meta: true,
        articles: {
          skip: skip,
          take: take,
          include: {
            article_tags: {
              include: {
                tag: {
                  select: {
                    name: true,
                    slug: true,
                  }
                }
              }
            }
          },

        }
      }
    })

    if (!site) {
      return NextResponse.json(
        { error: 'Site not found' },
        { status: 404 }
      )
    }

    // // add 9 dummy articles
    // if (process.env.APP_ENV !== 'production') {
    //   site.articles = [
    //     ...site.articles,
    //     ...getDummyArticles(9).map(article => ({
    //       ...article,
    //       id: String(article.id),
    //       article_tags: [],
    //       status: true,
    //       created_at: new Date(),
    //       updated_at: new Date(),
    //       site_id: site.id,
    //       image_url: article.featuredImage || null,
    //       meta_title: article.title,
    //       meta_description: article.metaDescription,
    //       meta_keywords: article.tags ? article.tags.join(', ') : null,
    //       published: true,
    //     })),
    //   ];
    // }
    return NextResponse.json(site)

  } catch (error) {
    console.error('Error fetching site data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 