import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';


export async function GET(req: NextRequest) {
	// get the slug from the path parameter
	const { searchParams } = new URL(req.url);
    const domain = searchParams.get('domain');
	const limit = searchParams.get('limit');
	const take = Number(limit);

	if (!domain) {
		return NextResponse.json(
			{ error: 'Domain parameter is required' },
			{ status: 400 }
		)
	}

	// Query the database for site data
	const site = await prisma.site.findUnique({
		where: { domain }
	})

	if (!site) {
		return NextResponse.json(
			{ error: 'Site not found' },
			{ status: 404 }
		)
	}

	const tags = await prisma.tag.findMany({
        select: {
            id: true,
            name: true,
            slug: true
        },
		where: { 
			article_tags: {
				every: {
					article: {
						site: {
							domain: domain
						}
					}
				}
			}
		},
		take: take
	});
    console.log(tags)
	return NextResponse.json({ success: true, tags: tags });
}