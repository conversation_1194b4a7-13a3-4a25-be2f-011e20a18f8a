import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const sites = await prisma.site.findMany({
      include: {
        site_meta: true,
        articles: true,
        static_pages: true,
        user: {
          select: {
            id: true,
            email: true,
            nicename: true,
          },
        },
      },
    });
    return NextResponse.json(sites);
  } catch (error) {
    console.error('Error fetching all sites:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
