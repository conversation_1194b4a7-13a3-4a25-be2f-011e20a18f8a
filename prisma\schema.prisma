generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id         String   @id @default(uuid())
  email      String   @unique
  password   String
  nicename   String?  // nullable
  status     Boolean  @default(true)
  role       Role     @relation(fields: [roleId], references: [id])
  roleId     String
  refresh_token String?  // nullable
  sites      Site[]
  metas      UserMeta[]
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
}

model UserMeta {
  id         String   @id @default(uuid())
  user       User     @relation(fields: [user_id], references: [id])
  user_id    String
  meta_key   String
  meta_value String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@unique([user_id, meta_key])
}

model Role {
  id          String       @id @default(uuid())
  name        String       @unique
  status      Boolean      @default(true)
  users       User[]
  permissions Permission[]
  created_at  DateTime     @default(now())
  updated_at  DateTime     @updatedAt
}

model Permission {
  id         String   @id @default(uuid())
  name       String
  role       Role     @relation(fields: [roleId], references: [id])
  roleId     String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
}

model Site {
  id           String        @id @default(uuid())
  user         User          @relation(fields: [user_id], references: [id])
  user_id      String
  domain       String        @unique
  site_name    String
  status       Boolean       @default(true)
  site_meta    SiteMeta[]
  articles     Article[]
  static_pages StaticPage[]
  created_at   DateTime      @default(now())
  updated_at   DateTime      @updatedAt
}

model SiteMeta {
  id         String   @id @default(uuid())
  site       Site     @relation(fields: [site_id], references: [id])
  site_id    String
  meta_key   String
  meta_value String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@unique([site_id, meta_key])
}

model Article {
  id               String   @id @default(uuid())
  site             Site     @relation(fields: [site_id], references: [id])
  site_id          String
  title            String
  slug             String
  content          String
  status           Boolean  @default(true)
  image_url        String?  // nullable
  meta_title       String?  // nullable
  meta_description String?  // nullable
  meta_keywords    String?  // nullable
  published        Boolean  @default(false)
  article_tags     ArticleTag[]
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  @@unique([site_id, slug])
}

enum StaticPageType {
  HOME
  ABOUT
  PRIVACY
  ADVERTISE
  CONTACT
  TERMS
}

model StaticPage {
  id         String         @id @default(uuid())
  site       Site           @relation(fields: [site_id], references: [id])
  site_id    String
  page_type  StaticPageType
  title      String
  content    String?
  data       Json?
  status     Boolean        @default(true)
  created_at DateTime       @default(now())
  updated_at DateTime       @updatedAt

  @@unique([site_id, page_type])
}

model Tag {
  id            String    @id @default(uuid())
  name          String
  slug          String    @unique
  description   String?
  status        Boolean   @default(true)
  article_tags  ArticleTag[]
  created_at    DateTime  @default(now())
  updated_at    DateTime  @updatedAt
}

model ArticleTag {
  id          String   @id @default(uuid())
  tag         Tag      @relation(fields: [tag_id], references: [id])
  tag_id      String
  article     Article  @relation(fields: [article_id], references: [id])
  article_id  String
  status      Boolean  @default(true)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
}
