@keyframes fadeIn {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@keyframes zoomIn {
    0% {
        transform: scale(.95)
    }

    to {
        transform: scale(1)
    }
}

@keyframes zoomOut {
    0% {
        transform: scale(1)
    }

    to {
        transform: scale(.95)
    }
}

@keyframes zoom {
    0% {
        opacity: 0;
        transform: scale(.95)
    }

    to {
        opacity: 1;
        transform: scale(1)
    }
}

@keyframes slideFromTop {
    0% {
        transform: translateY(-.5rem)
    }

    to {
        transform: translateY(0)
    }
}

@keyframes slideFromRight {
    0% {
        transform: translateX(.5rem)
    }

    to {
        transform: translateX(0)
    }
}

@keyframes slideFromLeft {
    0% {
        transform: translateX(-.5rem)
    }

    to {
        transform: translateX(0)
    }
}

@keyframes slideFromBottom {
    0% {
        transform: translateY(.5rem)
    }

    to {
        transform: translateY(0)
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

:root {
    overflow-wrap: break-word;
    text-size-adjust: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    --tt-gray-light-a-50: rgba(56,56,56,0.04);
    --tt-gray-light-a-100: rgba(15,22,36,0.05);
    --tt-gray-light-a-200: rgba(37,39,45,0.1);
    --tt-gray-light-a-300: rgba(47,50,55,0.2);
    --tt-gray-light-a-400: rgba(40,44,51,0.42);
    --tt-gray-light-a-500: rgba(52,55,60,0.64);
    --tt-gray-light-a-600: rgba(36,39,46,0.78);
    --tt-gray-light-a-700: rgba(35,37,42,0.87);
    --tt-gray-light-a-800: rgba(30,32,36,0.95);
    --tt-gray-light-a-900: rgba(29,30,32,0.98);
    --tt-gray-light-50: rgba(250,250,250,1);
    --tt-gray-light-100: rgba(244,244,245,1);
    --tt-gray-light-200: rgba(234,234,235,1);
    --tt-gray-light-300: rgba(213,214,215,1);
    --tt-gray-light-400: rgba(166,167,171,1);
    --tt-gray-light-500: rgba(125,127,130,1);
    --tt-gray-light-600: rgba(83,86,90,1);
    --tt-gray-light-700: rgba(64,65,69,1);
    --tt-gray-light-800: rgba(44,45,48,1);
    --tt-gray-light-900: rgba(34,35,37,1);
    --tt-gray-dark-a-50: rgba(232,232,253,0.05);
    --tt-gray-dark-a-100: rgba(231,231,243,0.07);
    --tt-gray-dark-a-200: rgba(238,238,246,0.11);
    --tt-gray-dark-a-300: rgba(239,239,245,0.22);
    --tt-gray-dark-a-400: rgba(244,244,255,0.37);
    --tt-gray-dark-a-500: rgba(236,238,253,0.5);
    --tt-gray-dark-a-600: rgba(247,247,253,0.64);
    --tt-gray-dark-a-700: rgba(251,251,254,0.75);
    --tt-gray-dark-a-800: rgba(253,253,253,0.88);
    --tt-gray-dark-a-900: rgba(255,255,255,0.96);
    --tt-gray-dark-50: rgba(25,25,26,1);
    --tt-gray-dark-100: rgba(32,32,34,1);
    --tt-gray-dark-200: rgba(45,45,47,1);
    --tt-gray-dark-300: rgba(70,70,73,1);
    --tt-gray-dark-400: rgba(99,99,105,1);
    --tt-gray-dark-500: rgba(124,124,131,1);
    --tt-gray-dark-600: rgba(163,163,168,1);
    --tt-gray-dark-700: rgba(192,192,195,1);
    --tt-gray-dark-800: rgba(224,224,225,1);
    --tt-gray-dark-900: rgba(245,245,245,1);
    --tt-brand-color-50: rgba(239,238,255,1);
    --tt-brand-color-100: rgba(222,219,255,1);
    --tt-brand-color-200: rgba(195,189,255,1);
    --tt-brand-color-300: rgba(157,138,255,1);
    --tt-brand-color-400: rgba(122,82,255,1);
    --tt-brand-color-500: rgba(98,41,255,1);
    --tt-brand-color-600: rgba(84,0,229,1);
    --tt-brand-color-700: rgba(75,0,204,1);
    --tt-brand-color-800: rgba(56,0,153,1);
    --tt-brand-color-900: rgba(43,25,102,1);
    --tt-brand-color-950: hsla(257,100%,9%,1);
    --tt-color-green-inc-5: hsla(129,100%,97%,1);
    --tt-color-green-inc-4: hsla(129,100%,92%,1);
    --tt-color-green-inc-3: hsla(131,100%,86%,1);
    --tt-color-green-inc-2: hsla(133,98%,78%,1);
    --tt-color-green-inc-1: hsla(137,99%,70%,1);
    --tt-color-green-base: hsla(147,99%,50%,1);
    --tt-color-green-dec-1: hsla(147,97%,41%,1);
    --tt-color-green-dec-2: hsla(146,98%,32%,1);
    --tt-color-green-dec-3: hsla(146,100%,24%,1);
    --tt-color-green-dec-4: hsla(144,100%,16%,1);
    --tt-color-green-dec-5: hsla(140,100%,9%,1);
    --tt-color-yellow-inc-5: hsla(50,100%,97%,1);
    --tt-color-yellow-inc-4: hsla(50,100%,91%,1);
    --tt-color-yellow-inc-3: hsla(50,100%,84%,1);
    --tt-color-yellow-inc-2: hsla(50,100%,77%,1);
    --tt-color-yellow-inc-1: hsla(50,100%,68%,1);
    --tt-color-yellow-base: hsla(52,100%,50%,1);
    --tt-color-yellow-dec-1: hsla(52,100%,41%,1);
    --tt-color-yellow-dec-2: hsla(52,100%,32%,1);
    --tt-color-yellow-dec-3: hsla(52,100%,24%,1);
    --tt-color-yellow-dec-4: hsla(51,100%,16%,1);
    --tt-color-yellow-dec-5: hsla(50,100%,9%,1);
    --tt-color-red-inc-5: hsla(11,100%,96%,1);
    --tt-color-red-inc-4: hsla(11,100%,88%,1);
    --tt-color-red-inc-3: hsla(10,100%,80%,1);
    --tt-color-red-inc-2: hsla(9,100%,73%,1);
    --tt-color-red-inc-1: hsla(7,100%,64%,1);
    --tt-color-red-base: hsla(7,100%,54%,1);
    --tt-color-red-dec-1: hsla(7,100%,41%,1);
    --tt-color-red-dec-2: hsla(5,100%,32%,1);
    --tt-color-red-dec-3: hsla(4,100%,24%,1);
    --tt-color-red-dec-4: hsla(3,100%,16%,1);
    --tt-color-red-dec-5: hsla(1,100%,9%,1);
    --white: rgba(255,255,255,1);
    --black: rgba(14,14,17,1);
    --transparent: rgba(255,255,255,0);
    --tt-shadow-elevated-md: 0px 16px 48px 0px rgba(17,24,39,0.04),0px 12px 24px 0px rgba(17,24,39,0.04),0px 6px 8px 0px rgba(17,24,39,0.02),0px 2px 3px 0px rgba(17,24,39,0.02);
    --tt-radius-xxs: 0.125rem;
    --tt-radius-xs: 0.25rem;
    --tt-radius-sm: 0.375rem;
    --tt-radius-md: 0.5rem;
    --tt-radius-lg: 0.75rem;
    --tt-radius-xl: 1rem;
    --tt-transition-duration-short: 0.1s;
    --tt-transition-duration-default: 0.2s;
    --tt-transition-duration-long: 0.64s;
    --tt-transition-easing-default: cubic-bezier(0.46,0.03,0.52,0.96);
    --tt-transition-easing-cubic: cubic-bezier(0.65,0.05,0.36,1);
    --tt-transition-easing-quart: cubic-bezier(0.77,0,0.18,1);
    --tt-transition-easing-circ: cubic-bezier(0.79,0.14,0.15,0.86);
    --tt-transition-easing-back: cubic-bezier(0.68,-0.55,0.27,1.55);
    --tt-accent-contrast: 8%;
    --tt-destructive-contrast: 8%;
    --tt-foreground-contrast: 8%
}

:root,:root *,:root :after,:root :before {
    box-sizing: border-box;
    transition: none var(--tt-transition-duration-default) var(--tt-transition-easing-default)
}

:root {
    --tt-bg-color: var(--white);
    --tt-border-color: var(--tt-gray-light-a-200);
    --tt-border-color-tint: var(--tt-gray-light-a-100);
    --tt-sidebar-bg-color: var(--tt-gray-light-100);
    --tt-scrollbar-color: var(--tt-gray-light-a-200);
    --tt-cursor-color: var(--tt-brand-color-500);
    --tt-selection-color: rgba(157,138,255,0.2);
    --tt-card-bg-color: var(--white);
    --tt-card-border-color: var(--tt-gray-light-a-100)
}

.dark {
    --tt-bg-color: var(--black);
    --tt-border-color: var(--tt-gray-dark-a-200);
    --tt-border-color-tint: var(--tt-gray-dark-a-100);
    --tt-sidebar-bg-color: var(--tt-gray-dark-100);
    --tt-scrollbar-color: var(--tt-gray-dark-a-200);
    --tt-cursor-color: var(--tt-brand-color-400);
    --tt-selection-color: rgba(122,82,255,0.2);
    --tt-card-bg-color: var(--tt-gray-dark-50);
    --tt-card-border-color: var(--tt-gray-dark-a-50);
    --tt-shadow-elevated-md: 0px 16px 48px 0px rgba(0,0,0,0.5),0px 12px 24px 0px rgba(0,0,0,0.24),0px 6px 8px 0px rgba(0,0,0,0.22),0px 2px 3px 0px rgba(0,0,0,0.12)
}

:root {
    --tt-color-text-gray: hsl(45,2%,46%);
    --tt-color-text-brown: hsl(19,31%,47%);
    --tt-color-text-orange: hsl(30,89%,45%);
    --tt-color-text-yellow: hsl(38,62%,49%);
    --tt-color-text-green: hsl(148,32%,39%);
    --tt-color-text-blue: hsl(202,54%,43%);
    --tt-color-text-purple: hsl(274,32%,54%);
    --tt-color-text-pink: hsl(328,49%,53%);
    --tt-color-text-red: hsl(2,62%,55%);
    --tt-color-text-gray-contrast: hsla(39,26%,26%,0.15);
    --tt-color-text-brown-contrast: hsla(18,43%,69%,0.35);
    --tt-color-text-orange-contrast: hsla(24,73%,55%,0.27);
    --tt-color-text-yellow-contrast: hsla(44,82%,59%,0.39);
    --tt-color-text-green-contrast: hsla(126,29%,60%,0.27);
    --tt-color-text-blue-contrast: hsla(202,54%,59%,0.27);
    --tt-color-text-purple-contrast: hsla(274,37%,64%,0.27);
    --tt-color-text-pink-contrast: hsla(331,60%,71%,0.27);
    --tt-color-text-red-contrast: hsla(8,79%,79%,0.4)
}

.dark {
    --tt-color-text-gray: hsl(0,0%,61%);
    --tt-color-text-brown: hsl(18,35%,58%);
    --tt-color-text-orange: hsl(25,53%,53%);
    --tt-color-text-yellow: hsl(36,54%,55%);
    --tt-color-text-green: hsl(145,32%,47%);
    --tt-color-text-blue: hsl(202,64%,52%);
    --tt-color-text-purple: hsl(270,55%,62%);
    --tt-color-text-pink: hsl(329,57%,58%);
    --tt-color-text-red: hsl(1,69%,60%);
    --tt-color-text-gray-contrast: hsla(0,0%,100%,0.09);
    --tt-color-text-brown-contrast: hsla(17,45%,50%,0.25);
    --tt-color-text-orange-contrast: hsla(27,82%,53%,0.2);
    --tt-color-text-yellow-contrast: hsla(35,49%,47%,0.2);
    --tt-color-text-green-contrast: hsla(151,55%,39%,0.2);
    --tt-color-text-blue-contrast: hsla(202,54%,43%,0.2);
    --tt-color-text-purple-contrast: hsla(271,56%,60%,0.18);
    --tt-color-text-pink-contrast: hsla(331,67%,58%,0.22);
    --tt-color-text-red-contrast: hsla(0,67%,60%,0.25)
}

:root {
    --tt-color-highlight-yellow: #fef9c3;
    --tt-color-highlight-green: #dcfce7;
    --tt-color-highlight-blue: #e0f2fe;
    --tt-color-highlight-purple: #f3e8ff;
    --tt-color-highlight-red: #ffe4e6;
    --tt-color-highlight-gray: rgb(248,248,247);
    --tt-color-highlight-brown: rgb(244,238,238);
    --tt-color-highlight-orange: rgb(251,236,221);
    --tt-color-highlight-pink: rgb(252,241,246);
    --tt-color-highlight-yellow-contrast: #fbe604;
    --tt-color-highlight-green-contrast: #c7fad8;
    --tt-color-highlight-blue-contrast: #ceeafd;
    --tt-color-highlight-purple-contrast: #e4ccff;
    --tt-color-highlight-red-contrast: #ffccd0;
    --tt-color-highlight-gray-contrast: rgba(84,72,49,0.15);
    --tt-color-highlight-brown-contrast: rgba(210,162,141,0.35);
    --tt-color-highlight-orange-contrast: rgba(224,124,57,0.27);
    --tt-color-highlight-pink-contrast: rgba(225,136,179,0.27)
}

.dark {
    --tt-color-highlight-yellow: #6b6524;
    --tt-color-highlight-green: #509568;
    --tt-color-highlight-blue: #6e92aa;
    --tt-color-highlight-purple: #583e74;
    --tt-color-highlight-red: #743e42;
    --tt-color-highlight-gray: rgb(47,47,47);
    --tt-color-highlight-brown: rgb(74,50,40);
    --tt-color-highlight-orange: rgb(92,59,35);
    --tt-color-highlight-pink: rgb(78,44,60);
    --tt-color-highlight-yellow-contrast: #58531e;
    --tt-color-highlight-green-contrast: #47855d;
    --tt-color-highlight-blue-contrast: #5e86a1;
    --tt-color-highlight-purple-contrast: #4c3564;
    --tt-color-highlight-red-contrast: #643539;
    --tt-color-highlight-gray-contrast: rgba(255,255,255,0.094);
    --tt-color-highlight-brown-contrast: rgba(184,101,69,0.25);
    --tt-color-highlight-orange-contrast: rgba(233,126,37,0.2);
    --tt-color-highlight-pink-contrast: rgba(220,76,145,0.22)
}

*,:after,:before {
    box-sizing: border-box;
    border: 0 solid #e5e7eb
}

:host,html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    /* font-family: ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji; */
    font-feature-settings: normal;
    font-variation-settings: normal;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

body {
    margin: 0;
    line-height: inherit
}

hr {
    height: 0;
    color: inherit;
    border-top-width: 1px
}

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1,h2,h3,h4,h5,h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b,strong {
    font-weight: bolder
}

code,kbd,pre,samp {
    /* font-family: ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace; */
    font-feature-settings: normal;
    font-variation-settings: normal;
    font-size: 1em
}

small {
    font-size: 80%
}

sub,sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse
}

button,input,optgroup,select,textarea {
    /* font-family: inherit; */
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    color: inherit;
    margin: 0;
    padding: 0
}

button,select {
    text-transform: none
}

button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]) {
    -moz-appearance: button;
    appearance: button;
    -webkit-appearance: button;
    background-color: rgba(0,0,0,0);
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button,::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -moz-appearance: textfield;
    appearance: textfield;
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre {
    margin: 0
}

fieldset {
    margin: 0
}

fieldset,legend {
    padding: 0
}

menu,ol,ul {
    list-style: none;
    margin: 0;
    padding: 0
}

dialog {
    padding: 0
}

textarea {
    resize: vertical
}

input::placeholder,textarea::placeholder {
    opacity: 1;
    color: #9ca3af
}

[role=button],button {
    cursor: pointer
}

:disabled {
    cursor: default
}

audio,canvas,embed,iframe,img,object,svg,video {
    display: block
}

img,video {
    max-width: 100%;
    height: auto
}

[hidden]:where(:not([hidden=until-found])) {
    display: none
}


.tiptap-tooltip {
    --tt-tooltip-bg: var(--tt-gray-light-900);
    --tt-tooltip-text: var(--white);
    --tt-kbd: var(--tt-gray-dark-a-400)
}

.dark .tiptap-tooltip {
    --tt-tooltip-bg: var(--white);
    --tt-tooltip-text: var(--tt-gray-light-600);
    --tt-kbd: var(--tt-gray-light-a-400)
}

.tiptap-tooltip {
    z-index: 200;
    overflow: hidden;
    border-radius: var(--tt-radius-md,.375rem);
    background-color: var(--tt-tooltip-bg);
    padding: .375rem .5rem;
    font-size: .75rem;
    font-weight: 500;
    color: var(--tt-tooltip-text);
    box-shadow: 0 4px 6px -1px rgba(0,0,0,.1);
    text-align: center
}

.tiptap-tooltip kbd {
    display: inline-block;
    text-align: center;
    vertical-align: baseline;
    /* font-family: ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif; */
    text-transform: capitalize;
    color: var(--tt-kbd)
}

.tiptap-button {
    --tt-button-default-bg-color: var(--tt-gray-light-a-100);
    --tt-button-hover-bg-color: var(--tt-gray-light-200);
    --tt-button-active-bg-color: var(--tt-gray-light-a-200);
    --tt-button-active-bg-color-emphasized: var( --tt-brand-color-100 );
    --tt-button-active-bg-color-subdued: var( --tt-gray-light-a-200 );
    --tt-button-active-hover-bg-color: var(--tt-gray-light-300);
    --tt-button-active-hover-bg-color-emphasized: var( --tt-brand-color-200 );
    --tt-button-active-hover-bg-color-subdued: var( --tt-gray-light-a-300 );
    --tt-button-disabled-bg-color: var(--tt-gray-light-a-50);
    --tt-button-default-text-color: var(--tt-gray-light-a-600);
    --tt-button-hover-text-color: var(--tt-gray-light-a-900);
    --tt-button-active-text-color: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-emphasized: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-subdued: var(--tt-gray-light-a-900);
    --tt-button-disabled-text-color: var(--tt-gray-light-a-400);
    --tt-button-default-icon-color: var(--tt-gray-light-a-600);
    --tt-button-hover-icon-color: var(--tt-gray-light-a-900);
    --tt-button-active-icon-color: var(--tt-brand-color-500);
    --tt-button-active-icon-color-emphasized: var(--tt-brand-color-600);
    --tt-button-active-icon-color-subdued: var(--tt-gray-light-a-900);
    --tt-button-disabled-icon-color: var(--tt-gray-light-a-400);
    --tt-button-default-icon-sub-color: var(--tt-gray-light-a-400);
    --tt-button-hover-icon-sub-color: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color: var(--tt-gray-light-a-400);
    --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color-subdued: var(--tt-gray-light-a-400);
    --tt-button-disabled-icon-sub-color: var(--tt-gray-light-a-100);
    --tt-button-default-dropdown-arrows-color: var(--tt-gray-light-a-600);
    --tt-button-hover-dropdown-arrows-color: var(--tt-gray-light-a-700);
    --tt-button-active-dropdown-arrows-color: var(--tt-gray-light-a-600);
    --tt-button-active-dropdown-arrows-color-emphasized: var( --tt-gray-light-a-700 );
    --tt-button-active-dropdown-arrows-color-subdued: var(--tt-gray-light-a-600);
    --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-light-a-400)
}

.dark .tiptap-button {
    --tt-button-default-bg-color: var(--tt-gray-dark-a-100);
    --tt-button-hover-bg-color: var(--tt-gray-dark-200);
    --tt-button-active-bg-color: var(--tt-gray-dark-a-200);
    --tt-button-active-bg-color-emphasized: var( --tt-brand-color-900 );
    --tt-button-active-bg-color-subdued: var( --tt-gray-dark-a-200 );
    --tt-button-active-hover-bg-color: var(--tt-gray-dark-300);
    --tt-button-active-hover-bg-color-emphasized: var( --tt-brand-color-800 );
    --tt-button-active-hover-bg-color-subdued: var( --tt-gray-dark-a-300 );
    --tt-button-disabled-bg-color: var(--tt-gray-dark-a-50);
    --tt-button-default-text-color: var(--tt-gray-dark-a-600);
    --tt-button-hover-text-color: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color-emphasized: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color-subdued: var(--tt-gray-dark-a-900);
    --tt-button-disabled-text-color: var(--tt-gray-dark-a-300);
    --tt-button-default-icon-color: var(--tt-gray-dark-a-600);
    --tt-button-hover-icon-color: var(--tt-gray-dark-a-900);
    --tt-button-active-icon-color: var(--tt-brand-color-400);
    --tt-button-active-icon-color-emphasized: var(--tt-brand-color-400);
    --tt-button-active-icon-color-subdued: var(--tt-gray-dark-a-900);
    --tt-button-disabled-icon-color: var(--tt-gray-dark-a-400);
    --tt-button-default-icon-sub-color: var(--tt-gray-dark-a-300);
    --tt-button-hover-icon-sub-color: var(--tt-gray-dark-a-400);
    --tt-button-active-icon-sub-color: var(--tt-gray-dark-a-300);
    --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-dark-a-400);
    --tt-button-active-icon-sub-color-subdued: var(--tt-gray-dark-a-300);
    --tt-button-disabled-icon-sub-color: var(--tt-gray-dark-a-100);
    --tt-button-default-dropdown-arrows-color: var(--tt-gray-dark-a-600);
    --tt-button-hover-dropdown-arrows-color: var(--tt-gray-dark-a-700);
    --tt-button-active-dropdown-arrows-color: var(--tt-gray-dark-a-600);
    --tt-button-active-dropdown-arrows-color-emphasized: var( --tt-gray-dark-a-700 );
    --tt-button-active-dropdown-arrows-color-subdued: var(--tt-gray-dark-a-600);
    --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-dark-a-400)
}

.tiptap-button[data-style=ghost] {
    --tt-button-default-bg-color: var(--transparent);
    --tt-button-hover-bg-color: var(--tt-gray-light-200);
    --tt-button-active-bg-color: var(--tt-gray-light-a-100);
    --tt-button-active-bg-color-emphasized: var( --tt-brand-color-100 );
    --tt-button-active-bg-color-subdued: var( --tt-gray-light-a-100 );
    --tt-button-active-hover-bg-color: var(--tt-gray-light-200);
    --tt-button-active-hover-bg-color-emphasized: var( --tt-brand-color-200 );
    --tt-button-active-hover-bg-color-subdued: var( --tt-gray-light-a-200 );
    --tt-button-disabled-bg-color: var(--transparent);
    --tt-button-default-text-color: var(--tt-gray-light-a-600);
    --tt-button-hover-text-color: var(--tt-gray-light-a-900);
    --tt-button-active-text-color: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-emphasized: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-subdued: var(--tt-gray-light-a-900);
    --tt-button-disabled-text-color: var(--tt-gray-light-a-400);
    --tt-button-default-icon-color: var(--tt-gray-light-a-600);
    --tt-button-hover-icon-color: var(--tt-gray-light-a-900);
    --tt-button-active-icon-color: var(--tt-brand-color-500);
    --tt-button-active-icon-color-emphasized: var(--tt-brand-color-600);
    --tt-button-active-icon-color-subdued: var(--tt-gray-light-a-900);
    --tt-button-disabled-icon-color: var(--tt-gray-light-a-400);
    --tt-button-default-icon-sub-color: var(--tt-gray-light-a-400);
    --tt-button-hover-icon-sub-color: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color: var(--tt-gray-light-a-400);
    --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color-subdued: var(--tt-gray-light-a-400);
    --tt-button-disabled-icon-sub-color: var(--tt-gray-light-a-100);
    --tt-button-default-dropdown-arrows-color: var(--tt-gray-light-a-600);
    --tt-button-hover-dropdown-arrows-color: var(--tt-gray-light-a-700);
    --tt-button-active-dropdown-arrows-color: var(--tt-gray-light-a-600);
    --tt-button-active-dropdown-arrows-color-emphasized: var( --tt-gray-light-a-700 );
    --tt-button-active-dropdown-arrows-color-subdued: var( --tt-gray-light-a-600 );
    --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-light-a-400)
}

.dark .tiptap-button[data-style=ghost] {
    --tt-button-default-bg-color: var(--transparent);
    --tt-button-hover-bg-color: var(--tt-gray-dark-200);
    --tt-button-active-bg-color: var(--tt-gray-dark-a-100);
    --tt-button-active-bg-color-emphasized: var( --tt-brand-color-900 );
    --tt-button-active-bg-color-subdued: var( --tt-gray-dark-a-100 );
    --tt-button-active-hover-bg-color: var(--tt-gray-dark-200);
    --tt-button-active-hover-bg-color-emphasized: var( --tt-brand-color-800 );
    --tt-button-active-hover-bg-color-subdued: var( --tt-gray-dark-a-200 );
    --tt-button-disabled-bg-color: var(--transparent);
    --tt-button-default-text-color: var(--tt-gray-dark-a-600);
    --tt-button-hover-text-color: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color-emphasized: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color-subdued: var(--tt-gray-dark-a-900);
    --tt-button-disabled-text-color: var(--tt-gray-dark-a-300);
    --tt-button-default-icon-color: var(--tt-gray-dark-a-600);
    --tt-button-hover-icon-color: var(--tt-gray-dark-a-900);
    --tt-button-active-icon-color: var(--tt-brand-color-400);
    --tt-button-active-icon-color-emphasized: var(--tt-brand-color-300);
    --tt-button-active-icon-color-subdued: var(--tt-gray-dark-a-900);
    --tt-button-disabled-icon-color: var(--tt-gray-dark-a-400);
    --tt-button-default-icon-sub-color: var(--tt-gray-dark-a-300);
    --tt-button-hover-icon-sub-color: var(--tt-gray-dark-a-400);
    --tt-button-active-icon-sub-color: var(--tt-gray-dark-a-300);
    --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-dark-a-400);
    --tt-button-active-icon-sub-color-subdued: var(--tt-gray-dark-a-300);
    --tt-button-disabled-icon-sub-color: var(--tt-gray-dark-a-100);
    --tt-button-default-dropdown-arrows-color: var(--tt-gray-dark-a-600);
    --tt-button-hover-dropdown-arrows-color: var(--tt-gray-dark-a-700);
    --tt-button-active-dropdown-arrows-color: var(--tt-gray-dark-a-600);
    --tt-button-active-dropdown-arrows-color-emphasized: var( --tt-gray-dark-a-700 );
    --tt-button-active-dropdown-arrows-color-subdued: var( --tt-gray-dark-a-600 );
    --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-dark-a-400)
}

.tiptap-button[data-style=primary] {
    --tt-button-default-bg-color: var(--tt-brand-color-500);
    --tt-button-hover-bg-color: var(--tt-brand-color-600);
    --tt-button-active-bg-color: var(--tt-brand-color-100);
    --tt-button-active-bg-color-emphasized: var( --tt-brand-color-100 );
    --tt-button-active-bg-color-subdued: var( --tt-brand-color-100 );
    --tt-button-active-hover-bg-color: var(--tt-brand-color-200);
    --tt-button-active-hover-bg-color-emphasized: var( --tt-brand-color-200 );
    --tt-button-active-hover-bg-color-subdued: var( --tt-brand-color-200 );
    --tt-button-disabled-bg-color: var(--tt-gray-light-a-100);
    --tt-button-default-text-color: var(--white);
    --tt-button-hover-text-color: var(--white);
    --tt-button-active-text-color: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-emphasized: var(--tt-gray-light-a-900);
    --tt-button-active-text-color-subdued: var(--tt-gray-light-a-900);
    --tt-button-disabled-text-color: var(--tt-gray-light-a-400);
    --tt-button-default-icon-color: var(--white);
    --tt-button-hover-icon-color: var(--white);
    --tt-button-active-icon-color: var(--tt-brand-color-600);
    --tt-button-active-icon-color-emphasized: var(--tt-brand-color-600);
    --tt-button-active-icon-color-subdued: var(--tt-brand-color-600);
    --tt-button-disabled-icon-color: var(--tt-gray-light-a-400);
    --tt-button-default-icon-sub-color: var(--tt-gray-dark-a-500);
    --tt-button-hover-icon-sub-color: var(--tt-gray-dark-a-500);
    --tt-button-active-icon-sub-color: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-light-a-500);
    --tt-button-active-icon-sub-color-subdued: var(--tt-gray-light-a-500);
    --tt-button-disabled-icon-sub-color: var(--tt-gray-light-a-100);
    --tt-button-default-dropdown-arrows-color: var(--white);
    --tt-button-hover-dropdown-arrows-color: var(--white);
    --tt-button-active-dropdown-arrows-color: var(--tt-gray-light-a-700);
    --tt-button-active-dropdown-arrows-color-emphasized: var( --tt-gray-light-a-700 );
    --tt-button-active-dropdown-arrows-color-subdued: var( --tt-gray-light-a-700 );
    --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-light-a-400)
}

.dark .tiptap-button[data-style=primary] {
    --tt-button-default-bg-color: var(--tt-brand-color-500);
    --tt-button-hover-bg-color: var(--tt-brand-color-600);
    --tt-button-active-bg-color: var(--tt-brand-color-900);
    --tt-button-active-bg-color-emphasized: var( --tt-brand-color-900 );
    --tt-button-active-bg-color-subdued: var( --tt-brand-color-900 );
    --tt-button-active-hover-bg-color: var(--tt-brand-color-800);
    --tt-button-active-hover-bg-color-emphasized: var( --tt-brand-color-800 );
    --tt-button-active-hover-bg-color-subdued: var( --tt-brand-color-800 );
    --tt-button-disabled-bg-color: var(--tt-gray-dark-a-100);
    --tt-button-default-text-color: var(--white);
    --tt-button-hover-text-color: var(--white);
    --tt-button-active-text-color: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color-emphasized: var(--tt-gray-dark-a-900);
    --tt-button-active-text-color-subdued: var(--tt-gray-dark-a-900);
    --tt-button-disabled-text-color: var(--tt-gray-dark-a-300);
    --tt-button-default-icon-color: var(--white);
    --tt-button-hover-icon-color: var(--white);
    --tt-button-active-icon-color: var(--tt-brand-color-400);
    --tt-button-active-icon-color-emphasized: var(--tt-brand-color-400);
    --tt-button-active-icon-color-subdued: var(--tt-brand-color-400);
    --tt-button-disabled-icon-color: var(--tt-gray-dark-a-300);
    --tt-button-default-icon-sub-color: var(--tt-gray-dark-a-400);
    --tt-button-hover-icon-sub-color: var(--tt-gray-dark-a-500);
    --tt-button-active-icon-sub-color: var(--tt-gray-dark-a-300);
    --tt-button-active-icon-sub-color-emphasized: var(--tt-gray-dark-a-400);
    --tt-button-active-icon-sub-color-subdued: var(--tt-gray-dark-a-300);
    --tt-button-disabled-icon-sub-color: var(--tt-gray-dark-a-100);
    --tt-button-default-dropdown-arrows-color: var(--white);
    --tt-button-hover-dropdown-arrows-color: var(--white);
    --tt-button-active-dropdown-arrows-color: var(--tt-gray-dark-a-600);
    --tt-button-active-dropdown-arrows-color-emphasized: var( --tt-gray-dark-a-600 );
    --tt-button-active-dropdown-arrows-color-subdued: var( --tt-gray-dark-a-600 );
    --tt-button-disabled-dropdown-arrows-color: var(--tt-gray-dark-a-400)
}

.tiptap-button-group {
    align-items: center;
    display: flex;
    gap: .125rem
}

.tiptap-button-group,.tiptap-button-group [data-orientation=vertical],.tiptap-button-group[data-orientation=vertical] {
    flex-direction: column
}

.tiptap-button-group [data-orientation=horizontal],.tiptap-button-group[data-orientation=horizontal] {
    flex-direction: row
}

.tiptap-button {
    font-size: .875rem;
    font-weight: 500;
    font-feature-settings: "salt" on,"cv01" on;
    line-height: 1.15;
    height: 2rem;
    min-width: 2rem;
    border: none;
    padding: .5rem;
    gap: .25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--tt-radius-lg,.75rem);
    transition-property: background,color,opacity;
    transition-duration: var(--tt-transition-duration-default);
    transition-timing-function: var(--tt-transition-easing-default)
}

.tiptap-button:focus-visible {
    outline: none
}

.tiptap-button[data-focus-visible=true],.tiptap-button[data-highlighted=true] {
    background-color: var(--tt-button-hover-bg-color);
    color: var(--tt-button-hover-text-color)
}

.tiptap-button[data-size=large] {
    font-size: .9375rem;
    height: 2.375rem;
    min-width: 2.375rem;
    padding: .625rem
}

.tiptap-button[data-size=small] {
    font-size: .75rem;
    line-height: 1.2;
    height: 1.5rem;
    min-width: 1.5rem;
    padding: .3125rem;
    border-radius: var(--tt-radius-md,.5rem)
}

.tiptap-button .tiptap-button-text {
    padding: 0 .125rem;
    flex-grow: 1;
    text-align: left;
    line-height: 1.5rem
}

.tiptap-button[data-text-trim=on] .tiptap-button-text {
    text-overflow: ellipsis;
    overflow: hidden
}

.tiptap-button .tiptap-button-dropdown-arrows,.tiptap-button .tiptap-button-dropdown-small,.tiptap-button .tiptap-button-icon,.tiptap-button .tiptap-button-icon-sub {
    pointer-events: none;
    flex-shrink: 0
}

.tiptap-button .tiptap-button-icon {
    width: 1rem;
    height: 1rem
}

.tiptap-button[data-size=large] .tiptap-button-icon {
    width: 1.125rem;
    height: 1.125rem
}

.tiptap-button[data-size=small] .tiptap-button-icon {
    width: .875rem;
    height: .875rem
}

.tiptap-button .tiptap-button-icon-sub {
    width: 1rem;
    height: 1rem
}

.tiptap-button[data-size=large] .tiptap-button-icon-sub {
    width: 1.125rem;
    height: 1.125rem
}

.tiptap-button[data-size=small] .tiptap-button-icon-sub {
    width: .875rem;
    height: .875rem
}

.tiptap-button .tiptap-button-dropdown-arrows {
    width: .75rem;
    height: .75rem
}

.tiptap-button[data-size=large] .tiptap-button-dropdown-arrows {
    width: .875rem;
    height: .875rem
}

.tiptap-button .tiptap-button-dropdown-small,.tiptap-button[data-size=small] .tiptap-button-dropdown-arrows {
    width: .625rem;
    height: .625rem
}

.tiptap-button[data-size=large] .tiptap-button-dropdown-small {
    width: .75rem;
    height: .75rem
}

.tiptap-button[data-size=small] .tiptap-button-dropdown-small {
    width: .5rem;
    height: .5rem
}

.tiptap-button:has(>svg):not(:has(>:not(svg))) {
    gap: .125rem
}

.tiptap-button:has(>svg):not(:has(>:not(svg)))[data-size=large],.tiptap-button:has(>svg):not(:has(>:not(svg)))[data-size=small] {
    gap: .125rem
}

.tiptap-button:has(>svg:nth-of-type(2)):has(>.tiptap-button-dropdown-small):not(:has(>svg:nth-of-type(3))):not(:has(>.tiptap-button-text)) {
    gap: 0;
    padding-right: .25rem
}

.tiptap-button:has(>svg:nth-of-type(2)):has(>.tiptap-button-dropdown-small):not(:has(>svg:nth-of-type(3))):not(:has(>.tiptap-button-text))[data-size=large] {
    padding-right: .375rem
}

.tiptap-button:has(>svg:nth-of-type(2)):has(>.tiptap-button-dropdown-small):not(:has(>svg:nth-of-type(3))):not(:has(>.tiptap-button-text))[data-size=small] {
    padding-right: .25rem
}

.tiptap-button .tiptap-button-emoji {
    width: 1rem;
    display: flex;
    justify-content: center
}

.tiptap-button[data-size=large] .tiptap-button-emoji {
    width: 1.125rem
}

.tiptap-button[data-size=small] .tiptap-button-emoji {
    width: .875rem
}

.tiptap-button {
    background-color: var(--tt-button-default-bg-color);
    color: var(--tt-button-default-text-color)
}

.tiptap-button .tiptap-button-icon {
    color: var(--tt-button-default-icon-color)
}

.tiptap-button .tiptap-button-icon-sub {
    color: var(--tt-button-default-icon-sub-color)
}

.tiptap-button .tiptap-button-dropdown-arrows,.tiptap-button .tiptap-button-dropdown-small {
    color: var(--tt-button-default-dropdown-arrows-color)
}

.tiptap-button:hover,.tiptap-button[data-active-item=true]:not([disabled]) {
    background-color: var(--tt-button-hover-bg-color);
    color: var(--tt-button-hover-text-color)
}

.tiptap-button:hover .tiptap-button-icon,.tiptap-button[data-active-item=true]:not([disabled]) .tiptap-button-icon {
    color: var(--tt-button-hover-icon-color)
}

.tiptap-button:hover .tiptap-button-icon-sub,.tiptap-button[data-active-item=true]:not([disabled]) .tiptap-button-icon-sub {
    color: var(--tt-button-hover-icon-sub-color)
}

.tiptap-button:hover .tiptap-button-dropdown-arrows,.tiptap-button:hover .tiptap-button-dropdown-small,.tiptap-button[data-active-item=true]:not([disabled]) .tiptap-button-dropdown-arrows,.tiptap-button[data-active-item=true]:not([disabled]) .tiptap-button-dropdown-small {
    color: var(--tt-button-hover-dropdown-arrows-color)
}

.tiptap-button[data-active-state=on]:not([disabled]),.tiptap-button[data-state=open]:not([disabled]) {
    background-color: var(--tt-button-active-bg-color);
    color: var(--tt-button-active-text-color)
}

.tiptap-button[data-active-state=on]:not([disabled]) .tiptap-button-icon,.tiptap-button[data-state=open]:not([disabled]) .tiptap-button-icon {
    color: var(--tt-button-active-icon-color)
}

.tiptap-button[data-active-state=on]:not([disabled]) .tiptap-button-icon-sub,.tiptap-button[data-state=open]:not([disabled]) .tiptap-button-icon-sub {
    color: var(--tt-button-active-icon-sub-color)
}

.tiptap-button[data-active-state=on]:not([disabled]) .tiptap-button-dropdown-arrows,.tiptap-button[data-active-state=on]:not([disabled]) .tiptap-button-dropdown-small,.tiptap-button[data-state=open]:not([disabled]) .tiptap-button-dropdown-arrows,.tiptap-button[data-state=open]:not([disabled]) .tiptap-button-dropdown-small {
    color: var(--tt-button-active-dropdown-arrows-color)
}

.tiptap-button[data-active-state=on]:not([disabled]):hover,.tiptap-button[data-state=open]:not([disabled]):hover {
    background-color: var(--tt-button-active-hover-bg-color)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=emphasized],.tiptap-button[data-state=open]:not([disabled])[data-appearance=emphasized] {
    background-color: var(--tt-button-active-bg-color-emphasized);
    color: var(--tt-button-active-text-color-emphasized)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=emphasized] .tiptap-button-icon,.tiptap-button[data-state=open]:not([disabled])[data-appearance=emphasized] .tiptap-button-icon {
    color: var(--tt-button-active-icon-color-emphasized)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=emphasized] .tiptap-button-icon-sub,.tiptap-button[data-state=open]:not([disabled])[data-appearance=emphasized] .tiptap-button-icon-sub {
    color: var(--tt-button-active-icon-sub-color-emphasized)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=emphasized] .tiptap-button-dropdown-arrows,.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=emphasized] .tiptap-button-dropdown-small,.tiptap-button[data-state=open]:not([disabled])[data-appearance=emphasized] .tiptap-button-dropdown-arrows,.tiptap-button[data-state=open]:not([disabled])[data-appearance=emphasized] .tiptap-button-dropdown-small {
    color: var(--tt-button-active-dropdown-arrows-color-emphasized)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=emphasized]:hover,.tiptap-button[data-state=open]:not([disabled])[data-appearance=emphasized]:hover {
    background-color: var(--tt-button-active-hover-bg-color-emphasized)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=subdued],.tiptap-button[data-state=open]:not([disabled])[data-appearance=subdued] {
    background-color: var(--tt-button-active-bg-color-subdued);
    color: var(--tt-button-active-text-color-subdued)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=subdued] .tiptap-button-icon,.tiptap-button[data-state=open]:not([disabled])[data-appearance=subdued] .tiptap-button-icon {
    color: var(--tt-button-active-icon-color-subdued)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=subdued] .tiptap-button-icon-sub,.tiptap-button[data-state=open]:not([disabled])[data-appearance=subdued] .tiptap-button-icon-sub {
    color: var(--tt-button-active-icon-sub-color-subdued)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=subdued] .tiptap-button-dropdown-arrows,.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=subdued] .tiptap-button-dropdown-small,.tiptap-button[data-state=open]:not([disabled])[data-appearance=subdued] .tiptap-button-dropdown-arrows,.tiptap-button[data-state=open]:not([disabled])[data-appearance=subdued] .tiptap-button-dropdown-small {
    color: var(--tt-button-active-dropdown-arrows-color-subdued)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=subdued]:hover,.tiptap-button[data-state=open]:not([disabled])[data-appearance=subdued]:hover {
    background-color: var(--tt-button-active-hover-bg-color-subdued)
}

.tiptap-button[data-active-state=on]:not([disabled])[data-appearance=subdued]:hover .tiptap-button-icon,.tiptap-button[data-state=open]:not([disabled])[data-appearance=subdued]:hover .tiptap-button-icon {
    color: var(--tt-button-active-icon-color-subdued)
}

.tiptap-button:disabled {
    background-color: var(--tt-button-disabled-bg-color);
    color: var(--tt-button-disabled-text-color)
}

.tiptap-button:disabled .tiptap-button-icon {
    color: var(--tt-button-disabled-icon-color)
}
