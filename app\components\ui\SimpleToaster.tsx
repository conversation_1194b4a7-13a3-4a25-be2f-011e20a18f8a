"use client";
import React, { createContext, useContext, useState, ReactNode } from "react";

type ToastType = { message: string; type?: "success" | "error" };

const ToastContext = createContext<(toast: ToastType) => void>(() => {});

export function useSimpleToast() {
  return useContext(ToastContext);
}

export function SimpleToasterProvider({ children }: { children: ReactNode }) {
  const [toast, setToast] = useState<ToastType | null>(null);

  const showToast = (toast: ToastType) => {
    setToast(toast);
    setTimeout(() => setToast(null), 4000);
  };

  return (
    <ToastContext.Provider value={showToast}>
      {children}
      {toast && (
        <div
          className={`fixed top-6 right-6 z-50 px-6 py-3 rounded shadow-lg text-white transition-all
            ${toast.type === "success" ? "bg-emerald-600" : "bg-red-700"}`}
        >
          {toast.message}
        </div>
      )}
    </ToastContext.Provider>
  );
}
