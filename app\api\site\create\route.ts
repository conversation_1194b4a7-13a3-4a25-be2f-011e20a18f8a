import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import jwt from 'jsonwebtoken';
import { createCloudflareDNSRecord, getCloudflareDomainZoneID, removeCloudflareDNSRecord } from '@/lib/cloudflare';
import { uploadToSpaces } from '@/lib/do-spaces';
import { prisma } from '@/lib/prisma';


const execAsync = promisify(exec);

// Function to save uploaded file
async function saveFile(file: File, directory: string): Promise<string> {
	const bytes = await file.arrayBuffer();
	const buffer = Buffer.from(bytes);

	// Create directory if it doesn't exist
	const uploadDir = path.join(process.cwd(), 'public', directory);
	if (!fs.existsSync(uploadDir)) {
		fs.mkdirSync(uploadDir, { recursive: true });
	}

	// Generate unique filename
	const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
	const filename = `${uniqueSuffix}-${file.name}`;
	const filepath = path.join(uploadDir, filename);

	// Save file
	fs.writeFileSync(filepath, buffer);

	// Return the public URL path
	return `/${directory}/${filename}`;
}

// Function to check if running with admin privileges
async function isAdmin(): Promise<boolean> {
	try {
		// Try to write to a protected directory
		const testPath = 'C:\\Windows\\System32\\drivers\\etc\\test.txt';
		fs.writeFileSync(testPath, 'test');
		fs.unlinkSync(testPath);
		return true;
	} catch (error) {
		return false;
	}
}

// Function to create local configuration for a domain
async function createLocalConfig(domain: string): Promise<boolean> {
	const hostsPath = 'C:\\Windows\\System32\\drivers\\etc\\hosts';
	const hostsEntry = `\n127.0.0.1\t${domain}`;

	try {
		// Check for admin privileges
		const hasAdmin = await isAdmin();
		if (!hasAdmin) {
			return false;
		}

		// Read current hosts file
		const hostsContent = fs.readFileSync(hostsPath, 'utf8');

		// Check if domain already exists in hosts file
		if (hostsContent.includes(domain)) {
			console.log(`Domain ${domain} already exists in hosts file`);
			return false;
		}

		// Append new domain to hosts file
		fs.appendFileSync(hostsPath, hostsEntry);

		// Flush DNS cache (Windows)
		try {
			await execAsync('ipconfig /flushdns');
			console.log('DNS cache flushed successfully');
		} catch (error) {
			console.warn('Failed to flush DNS cache:', error);
			// Continue even if DNS flush fails
		}

		console.log(`Added ${domain} to hosts file successfully`);
	} catch (error) {
		console.error('Error modifying hosts file:', error);
		return false;
	}
	return true;
}

// Function to remove domain from hosts file
async function removeFromHosts(domain: string): Promise<boolean> {
	const hostsPath = 'C:\\Windows\\System32\\drivers\\etc\\hosts';

	try {
		// Check for admin privileges
		const hasAdmin = await isAdmin();
		if (!hasAdmin) {
			console.warn('Administrator privileges required to remove domain from hosts file. Please remove the following entry manually:\n127.0.0.1 ' + domain);
			return false;
		}

		// Read current hosts file
		const hostsContent = fs.readFileSync(hostsPath, 'utf8');

		// Remove the domain entry
		const newContent = hostsContent.replace(new RegExp(`\n127.0.0.1 ${domain}`, 'g'), '');

		// Write back the modified content
		fs.writeFileSync(hostsPath, newContent);

		// Flush DNS cache
		try {
			await execAsync('ipconfig /flushdns');
		} catch (error) {
			console.warn('Failed to flush DNS cache during cleanup:', error);
		}
	} catch (error) {
		console.error('Error removing domain from hosts file:', error);
		// Don't throw error here since this is cleanup code
	}
	return true;
}

async function installSSLCertificate(domain: string): Promise<boolean> {
	try {
		// Run certbot to install SSL certificate for the domain
		const { stdout, stderr } = await execAsync(`sudo certbot --nginx -d ${domain} --non-interactive --agree-tos --register-unsafely-without-email`);
		console.log('Certbot output:', stdout, stderr);
		return true;
	} catch (error) {
		console.error('Error installing SSL certificate:', error);
		return false;
	}
}

export async function POST(req: NextRequest) {
	try {
		const formData = await req.formData();
		const domain = formData.get('domain') as string;
		const siteName = formData.get('siteName') as string;
		const tagline = formData.get('tagline') as string;
		const logo = formData.get('logo') as File;
		const favicon = formData.get('favicon') as File;
		const company = formData.get('company') as string;
		const companyName = formData.get('companyName') as string;
		const phone = formData.get('phone') as string;
		const email = formData.get('email') as string;
		const address = formData.get('address') as string;

		// Get token from cookies
		const token = req.cookies.get('token')?.value;
		if (!token) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Verify token and get user ID
		const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as { userId: string };
		const userId = decoded.userId;

		// Validate required fields
		if (!domain || !siteName || !tagline) {
			return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
		}

		// Check if domain already exists
		const existingSite = await prisma.site.findUnique({
			where: { domain },
		});
		if (existingSite) {
			return NextResponse.json({ error: 'Domain already exists' }, { status: 400 });
		}

		const zoneID = await getCloudflareDomainZoneID(domain);
		try {
			// Create nginx configuration for the domain
			if (process.env.APP_ENV !== 'production') {
				await createLocalConfig(domain);
			} else {

				// Verify domain by Cloudflare API
				if (!zoneID) {
					return NextResponse.json({ error: 'Invalid domain' }, { status: 400 });
				}

				const dnsRecord = await createCloudflareDNSRecord(zoneID);
				if (!dnsRecord) {
					return NextResponse.json({ error: 'Failed to create Cloudflare DNS record' }, { status: 500 });
				}
				const sslInstalled = await installSSLCertificate(domain);
				if (!sslInstalled) {
					return NextResponse.json({ error: 'Failed to install SSL certificate' }, { status: 500 });
				}
			}

			// Upload logo
			let logoUrl = '';
			if (logo) {
				const bytes = await logo.arrayBuffer();
				const buffer = Buffer.from(bytes);
				const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
				const filename = `${uniqueSuffix}-${logo.name}`;
				logoUrl = await uploadToSpaces(buffer, filename, 'logo', logo.type);
			}

			// Upload favicon
			let faviconUrl = '';
			if (favicon) {
				const bytes = await favicon.arrayBuffer();
				const buffer = Buffer.from(bytes);
				const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
				const filename = `${uniqueSuffix}-${favicon.name}`;
				faviconUrl = await uploadToSpaces(buffer, filename, 'favicon', favicon.type);
			}

			// Create site in database
			const site = await prisma.site.create({
				data: {
					domain,
					site_name: siteName,
					user_id: userId,
					site_meta: {
						create: [
							{ meta_key: 'tagline', meta_value: tagline },
							{ meta_key: 'company', meta_value: company || companyName },
							{ meta_key: 'phone', meta_value: phone },
							{ meta_key: 'email', meta_value: email },
							{ meta_key: 'address', meta_value: address },
							{ meta_key: 'logo_url', meta_value: logoUrl },
							{ meta_key: 'favicon_url', meta_value: faviconUrl },
						],
					},
				},
			});

			return NextResponse.json({
				success: true,
				siteId: site.id,
				message: 'Site created successfully and installed SSL certificate'
			});
		} catch (error) {
			console.error('Error creating configuration:', error);
			// If configuration fails, delete the site and its related records
			try {
				const site = await prisma.site.findUnique({
					where: { domain },
				});

				if (site) {
					// First delete all related site_meta records
					await prisma.siteMeta.deleteMany({
						where: { site_id: site.id }
					});

					// Then delete the site
					await prisma.site.delete({
						where: { id: site.id },
					});
				}

				// Remove domain from hosts file in development
				if (process.env.APP_ENV !== 'production') {
					await removeFromHosts(domain);
				} else {
					await removeCloudflareDNSRecord(zoneID);
				}
			} catch (deleteError) {
				console.error('Error cleaning up after configuration failure:', deleteError);
			}

			return NextResponse.json({
				error: 'Failed to create configuration. Please ensure you have the necessary permissions.'
			}, { status: 500 });
		}
	} catch (error) {
		console.error('Error creating site:', error);
		return NextResponse.json({
			error: 'An unexpected error occurred while creating the site. Please try again or contact support if the issue persists.'
		}, { status: 500 });
	}
}
