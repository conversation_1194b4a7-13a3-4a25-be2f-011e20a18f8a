import React from "react";
import "./globals.css";
import type { Metadata } from "next";
import Header from "./components/Header";
import Footer from "./components/Footer";
import { Poppins } from "next/font/google";
import { AuthProvider } from "@/app/context/AuthContext";
import { SimpleToasterProvider } from "./components/ui/SimpleToaster";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700"],
  variable: "--font-poppins",
});

export const metadata: Metadata = {
  title: "Adds AI",
  description: "AI-powered website creation platform",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={poppins.variable}>
      <body className="min-h-screen">
        <AuthProvider>
          <SimpleToasterProvider>
            <SidebarProvider>
              <AppSidebar />
              <SidebarInset>
                <Header />
                <div className="p-6 bg-gray-50 overflow-auto h-full">
                {children}
                </div>
                <Footer />
              </SidebarInset>
            </SidebarProvider>

          </SimpleToasterProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
