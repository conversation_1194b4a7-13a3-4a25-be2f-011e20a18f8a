"use server"

/**
 * Verify if a domain exists in Cloudflare.
 * @param {string} domain - The domain to verify.
 * @return {Promise<string | false>} - Returns Zone ID if the domain exists, false otherwise.
 */
export async function getCloudflareDomainZoneID(domain: string): Promise<string | false> {
    try {
        const response = await fetch(`https://api.cloudflare.com/client/v4/zones?name=${domain}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            console.error('Cloudflare API error:', response.statusText);
            return false;
        }

        const data = await response.json();
        return data.success && data.result.length > 0 ? data.result[0].id : false;
    } catch (error) {
        console.error('Error verifying domain with Cloudflare:', error);
        return false;
    }
    
}


/**
 * Create a Cloudflare DNS record.
 * @param {string} zoneID - The zone ID to create the record in.
 * @return {Promise<boolean>} - Returns true if the record is created, false otherwise.
 */
export async function createCloudflareDNSRecord(zoneID: string) {
    try {
        const response = await fetch(`https://api.cloudflare.com/client/v4/zones/${zoneID}/dns_records`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                "name": "@",
                "ttl": 3600,
                "type": "A",
                "comment": "Dynamic website generator (adds-ai)",
                "content": process.env.APP_IPv4,
                "proxied": true
            }),
        });

        if (!response.ok) {
            const data = await response.json();
            const errors = data.errors as Array<{code: number, message: string}>;
            let identicle = false;
            errors.map((error)=>{
                if ( error.code === 81058 ) identicle = true;
            });
            if(!identicle) {
                console.error('Cloudflare API error:', response.statusText);
                return false;
            }
        }
        return true;
    } catch (error) {
        console.error('Error creating Cloudflare DNS record:', error);
        return false;
    }
}


/**
 * Remove a Cloudflare DNS record.
 * @param {string} zoneID - The zone ID to remove the record from.
 * @return {Promise<boolean>} - Returns true if the record is removed, false otherwise.
 */
export async function removeCloudflareDNSRecord(zoneID: string|boolean) {
    if(!zoneID){
        return;
    }
    try {
        const response = await fetch(`https://api.cloudflare.com/client/v4/zones/${zoneID}/dns_records/1`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
                'Content-Type': 'application/json',
            },
        });
        if (!response.ok) {
            console.error('Cloudflare API error:', response.statusText);
            return false;
        }
        return true;
    } catch (error) {
        console.error('Error removing Cloudflare DNS record:', error);
        return false;
    }
}
