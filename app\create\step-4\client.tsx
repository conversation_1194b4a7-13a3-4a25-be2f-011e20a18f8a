"use client"
import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';

export default function Step4() {
  const router = useRouter();
  const [publishing, setPublishing] = useState(false);
  const [error, setError] = useState('');
  const searchParams = useSearchParams();
  const [siteId, setSiteId] = useState('');
  const [validationError, setValidationError] = useState('');
  const [isValidating, setIsValidating] = useState(true);

  useEffect(() => {
    const site_id = searchParams.get('siteId');
    if (site_id) {
      setSiteId(site_id as string);
    }
  }, [searchParams]);

  useEffect(() => {
    const validateSiteId = async () => {
      if (!siteId) {
        router.push('/create/step-1');
        return;
      }
      try {
        const res = await fetch('/api/site/validate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ siteId }),
        });
        if (!res.ok) {
          const data = await res.json();
          setValidationError(data.error || 'Invalid site ID');
          router.push('/create/step-1');
          return;
        }
        setIsValidating(false);
      } catch (error) {
        setValidationError('Failed to validate site');
        router.push('/create/step-1');
      }
    };

    if (siteId) {
      validateSiteId();
    }
  }, [siteId, router]);

  const handlePublish = async () => {
    setPublishing(true);
    setError('');
    try {
      // Replace with actual siteId
      const res = await fetch('/api/site/publish', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ siteId }),
      });
      const data = await res.json();
      if (data.success) {
        router.push(`/create/success?siteId=${siteId}`);
      } else {
        setError('Failed to publish site.');
      }
    } catch (err) {
      setError('Failed to publish site.');
    } finally {
      setPublishing(false);
    }
  };

  // Show loading state while validating
  if (isValidating) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Validating site...</p>
        </div>
      </div>
    );
  }

  // Show error if validation failed
  if (validationError) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">{validationError}</p>
          <button
            onClick={() => router.push('/create/step-1')}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Return to Step 1
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-24">
      <h1 className="text-4xl font-bold mb-8">Publish Your Site</h1>
      {error && <div className="text-red-500 mb-4">{error}</div>}
      <Button
        onClick={handlePublish}
        disabled={publishing}
        className='text-white'
      >
        {publishing ? 'Publishing...' : 'Publish Site'}
      </Button>
    </div>
  );
} 