import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { getDummyArticles } from '../../dummyData'; // Adjust path as needed
import { withTimeoutAndRetry } from '@/lib/withTimeoutAndRetry';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

function generateSlug(title: string) {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .trim()
    .replace(/\s+/g, '-');
}

export async function POST(req: NextRequest) {
  try {
    const { contentStyle, tone, language, title, articleId } = await req.json();
    const slug = generateSlug(title);

    // Return dummy in non-production mode
    if (process.env.APP_ENV !== 'production') {
      const dummy = getDummyArticles(1)[0];
      return NextResponse.json({
        article: {
          ...dummy,
          id: articleId,
          title,
          slug,
        },
        durationMs: 500,
      });
    }

    const systemPrompt = `You are a professional content writer and SEO strategist for ${language}.
      Generate a JSON response for the following article title with:
      - metaDescription (150-160 characters, optimized for click-through)
      - tags: 1-2 relevant keywords
      - content: An HTML text of 1000-2500 words in ${tone} tone and ${contentStyle} style (use SEO practices, H2/H3, proper formatting)
      - imagePrompt: vivid scene for a high-quality featured image

      Respond only in this exact JSON format:
      {
        "article": {
          "metaDescription": "",
          "tags": ["", ""],
          "content": "",
          "imagePrompt": ""
        }
      }

      Title: ${title}

      IMPORTANT: Return ONLY valid JSON. Do NOT include markdown, comments, or explanations. Ensure 'content' an HTML text only.`;

    const start = performance.now();

    const completion = await withTimeoutAndRetry(() =>
      openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'system', content: systemPrompt }],
        response_format: { type: 'json_object' },
        temperature: 0.7,
        max_tokens: 3500,
      })
    );

    const end = performance.now();
    const gptDurationMs = Math.round(end - start);

    const content = completion.choices[0].message.content;
    if (!content) throw new Error("OpenAI returned empty content");

    // Safely parse JSON
    let article = null;
    try {
      const parsed = JSON.parse(content);
      if (!parsed.article) throw new Error("Missing 'article' key in response");

      article = {
        ...parsed.article,
        id: articleId,
        title,
        slug,
      };
    } catch (err) {
      console.error("Error parsing OpenAI response:", content);
      throw new Error("Invalid JSON structure from OpenAI.");
    }

    return NextResponse.json({ article, durationMs: gptDurationMs }, { status: 200 });

  } catch (error) {
    console.error("Error generating article:", error);
    return NextResponse.json({ error: 'Failed to generate article' }, { status: 500 });
  }
}
